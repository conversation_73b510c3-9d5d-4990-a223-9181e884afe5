import { useTheme } from "../../contexts/ThemeContext";
import DarkModeIcon from "@mui/icons-material/DarkMode";
import LightModeIcon from "@mui/icons-material/LightMode";



const ThemeToggle = ({ className = "" }) => {
  const { theme, toggleTheme } = useTheme();
  
  const handleClick = () => {
    console.log("Theme toggle button clicked");
    console.log("Current theme:", theme);
    toggleTheme();
  };

  return (
    <button
      onClick={handleClick}
      aria-label="Toggle theme"
      title={theme === "light" ? "Switch to dark" : "Switch to light"}
      className={`p-2 rounded-full border transition-colors duration-200 ${className}`}
    >
      {theme === "light" ? <DarkModeIcon className="bg-white rounded-xl"/> : <LightModeIcon />}
    </button>
  );
};

export default ThemeToggle;
