import { useTheme } from "../../contexts/ThemeContext";
import DarkModeIcon from "@mui/icons-material/DarkMode";
import LightModeIcon from "@mui/icons-material/LightMode";

const ThemeToggle = ({ className = "" }) => {
  const { theme, toggleTheme } = useTheme();
  
  const handleClick = () => {
    console.log("Theme toggle button clicked");
    console.log("Current theme:", theme);
    toggleTheme();
  };

  return (
    <button
      onClick={handleClick}
      aria-label="Toggle theme"
      className={
        "p-2 rounded-full border transition-colors duration-200 " +
        "bg-gray-200 text-gray-800 hover:bg-gray-300 " +
        "dark:bg-gray-700 dark:text-yellow-200 dark:hover:bg-gray-600 " +
        className
      }
      title={theme === "light" ? "Switch to dark" : "Switch to light"}
    >
      {/* using Font Awesome since you already import it */}
      {theme === "light" ? (
        <DarkModeIcon />
      ) : (
        <LightModeIcon />
      )}
    </button>
  );
};

export default ThemeToggle;
