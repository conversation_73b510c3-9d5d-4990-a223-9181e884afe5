import React, { useState, useEffect , useRef } from 'react';
import darjeeling from '../assets/photos/dargeeling2.webp';
import gangtok from '../assets/photos/dooars.webp';
import northSikkim from '../assets/photos/plan2.png';
import card2 from '../assets/photos/card2.webp';
import nature2 from '../assets/photos/nature2.webp';
import { useNavigate, useLocation } from 'react-router-dom';
import forest from '../assets/photos/forest.webp';

const Plan = () => {
  const [selectedPersons, setSelectedPersons] = useState('');
  const [groupMembers, setGroupMembers] = useState([]);
  const [searchDestination, setSearchDestination] = useState('');
  const navigate = useNavigate();
  const location = useLocation();

  const packagesRef = useRef(null);

  // Extract destination from URL query parameters
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const destination = queryParams.get('destination');
    if (destination) {
      setSearchDestination(destination);
    }

    //persons
    const persons = queryParams.get('persons');
    if (persons) {
      setSelectedPersons(persons);
    }
    if (persons && persons !== 'Any') {
      const num = persons === '4+' ? 5 : parseInt(persons);
      const newMembers = Array(num).fill(0).map((_, i) => ({ id: Date.now() + i }));
      setGroupMembers(newMembers);
    }
    if (persons && packagesRef.current) {
      packagesRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    
  }, [location.search]);

  // We've replaced these functions with direct inline handlers in the number selector buttons

  const getFilteredPackages = () => {
    let filteredPackages = packages;

    // Filter by number of persons if selected
    if (selectedPersons) {
      filteredPackages = filteredPackages.filter(pkg => {
        if (selectedPersons > 4) {
          return pkg.no_of_persons === "4+";
        }
        return pkg.no_of_persons === selectedPersons.toString();
      });
    }

    // Filter by destination search term if provided
    if (searchDestination) {
      filteredPackages = filteredPackages.filter((pkg) =>
        pkg.location.toLowerCase().includes(searchDestination.toLowerCase())
      );
    }

    return filteredPackages;
  };

  // We're now applying the styles directly in the JSX

  const handleCheckDetails = (packageId) => {
    window.scrollTo(0, 0);
    navigate(`/package/${packageId}`);
  };

  const packages = [
    {
      id: 1,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "2",
      price: "₹15,999",
      image: darjeeling,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 2,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "2",
      price: "₹18,999",
      image: gangtok,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 3,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "2",
      price: "₹22,999",
      image: northSikkim,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 4,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "2",
      price: "₹25,999",
      image: card2,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 5,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "3",
      price: "₹12,999",
      image: gangtok,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 6,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "3",
      price: "₹18,999",
      image: northSikkim,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 7,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "3",
      price: "₹22,999",
      image: darjeeling,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 8,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "3",
      price: "₹25,999",
      image: card2,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 9,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "4",
      price: "₹14,999",
      image: northSikkim,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 10,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "4",
      price: "₹18,999",
      image: darjeeling,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 11,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "4",
      price: "₹25,999",
      image: gangtok,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 12,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "4",
      price: "₹28,999",
      image: card2,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 13,
      location: "Darjeeling",
      duration: "3D/2N",
      no_of_persons: "4+",
      price: "₹16,999",
      image: gangtok,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 14,
      location: "Darjeeling-Gangtok",
      duration: "4D/3N",
      no_of_persons: "4+",
      price: "₹20,999",
      image: darjeeling,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 15,
      location: "Gangtok-North Sikkim",
      duration: "5D/4N",
      no_of_persons: "4+",
      price: "₹28,999",
      image: gangtok,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    },
    {
      id: 16,
      location: "Darjeeling-Gangtok-North Sikkim",
      duration: "6D/5N",
      no_of_persons: "4+",
      price: "₹32,999",
      image: card2,
      points: {
        privatecar: "",
        hotel: "",
        sightseeing: "",
        support: "24/7"
      }
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pt-4">
      {/* Hero Section */}
      <div className="bg-gray-50 dark:bg-gray-900 pt-24 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Top Section */}
          <div className="text-center mb-12 py-4">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 font-serif">
              Find Your Perfect Travel Package
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto font-serif">
              Select your destination, group size, and preferences to discover the ideal North East India experience
            </p>
          </div>

          {/* Image Carousel */}
          <div className="relative rounded-2xl overflow-hidden shadow-xl mb-8">
            {/* Main Image */}
            <div className="h-[400px] relative">
              <img
                src={forest}
                alt="North East India"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent"></div>

              {/* Image Caption */}
              <div className="absolute bottom-0 left-0 p-8 max-w-lg">
                <h2 className="text-3xl font-bold text-white mb-2">North Sikkim</h2>
                <p className="text-white/90 mb-4">
                  Experience the majestic Himalayan landscapes and pristine lakes
                </p>
                <a
                  href="#packages"
                  className="inline-flex items-center px-5 py-2 bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors shadow-md"
                >
                  <span>View Packages</span>
                  <i className="fas fa-arrow-right ml-2"></i>
                </a>
              </div>

              {/* Carousel Navigation */}
              <div className="absolute bottom-8 right-8 flex space-x-2">
                <button className="w-3 h-3 rounded-full bg-white"></button>
                <button className="w-3 h-3 rounded-full bg-white/50"></button>
                <button className="w-3 h-3 rounded-full bg-white/50"></button>
                <button className="w-3 h-3 rounded-full bg-white/50"></button>
              </div>
            </div>

            {/* Thumbnail Strip */}
            <div className="flex bg-white border-t border-gray-200">
              <div className="w-1/4 p-4 cursor-pointer bg-gray-100">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                    <img
                      src={northSikkim}
                      alt="North Sikkim"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    North Sikkim
                  </span>
                </div>
              </div>
              <div className="w-1/4 p-4 cursor-pointer">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                    <img
                      src={darjeeling}
                      alt="Darjeeling"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Darjeeling
                  </span>
                </div>
              </div>
              <div className="w-1/4 p-4 cursor-pointer">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                    <img
                      src={gangtok}
                      alt="Gangtok"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Gangtok
                  </span>
                </div>
              </div>
              <div className="w-1/4 p-4 cursor-pointer">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                    <img
                      src={card2}
                      alt="Wildlife"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Wildlife
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
            <a
              href="#packages"
              className="flex items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100"
            >
              <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                <i className="fas fa-user text-gray-600"></i>
              </div>
              <span className="font-medium text-gray-800">No of Persons</span>
            </a>
            <a
              href="#packages"
              className="flex items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100"
            >
              <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                <i className="fas fa-star text-gray-600"></i>
              </div>
              <span className="font-medium text-gray-800">Popular Packages</span>
            </a>
            <a
              href="#packages"
              className="flex items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100"
            >
              <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                <i className="fas fa-users text-gray-600"></i>
              </div>
              <span className="font-medium text-gray-800">Family Trips</span>
            </a>
            <a
              href="#packages"
              className="flex items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100"
            >
              <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                <i className="fas fa-mountain text-gray-600"></i>
              </div>
              <span className="font-medium text-gray-800">Adventure Tours</span>
            </a>
            <a
              href="#custom"
              className="flex items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100"
            >
              <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                <i className="fas fa-magic text-gray-600"></i>
              </div>
              <span className="font-medium text-gray-800">Custom Packages</span>
            </a>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
        {/* Search Notification */}
        {searchDestination && (
          <div className="mb-8 bg-blue-50 border border-blue-200 rounded-md p-4 flex items-center justify-between">
            <div className="flex items-center">
              <i className="fas fa-search text-blue-500 mr-3"></i>
              <span className="text-blue-800">
                Showing packages for: <strong>{searchDestination}</strong>
              </span>
            </div>
            <button
              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
              onClick={() => setSearchDestination('')}
            >
              Clear Search
            </button>
          </div>
        )}

        {/* Traveler Selection */}
        <div id="packages" className="mb-12 bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <h2 className="text-xl font-bold text-gray-800 mb-6">Select Number of Travelers</h2>

          <div className="grid grid-cols-2 sm:grid-cols-5 gap-4">
            {[
              { value: 1, label: 'Solo', icon: 'fa-user' },
              { value: 2, label: 'Couple', icon: 'fa-heart' },
              { value: 3, label: 'Small Group', icon: 'fa-users' },
              { value: 4, label: 'Family', icon: 'fa-home' },
              { value: '4+', label: 'Large Group', icon: 'fa-user-friends' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => {
                  if (selectedPersons === option.value.toString()) {
                    setGroupMembers([]);
                    setSelectedPersons('');
                  } else {
                    const num = typeof option.value === 'number' ? option.value : 5;
                    const newMembers = Array(num).fill(0).map((_, i) => ({ id: Date.now() + i }));
                    setGroupMembers(newMembers);
                    setSelectedPersons(option.value.toString());
                  }
                }}
                className={`flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all ${
                  selectedPersons === option.value.toString()
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/50'
                }`}
              >
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                  selectedPersons === option.value.toString()
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-700'
                }`}>
                  <i className={`fas ${option.icon} text-xl`}></i>
                </div>
                <span className="font-medium">{option.label}</span>
                <span className="text-sm text-gray-700">{option.value} {option.value === 1 ? 'Person' : 'People'}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Package Filters */}
        <div className="mb-8 flex flex-wrap items-center justify-between gap-4" ref={packagesRef}>
          <h2 className="text-2xl font-bold text-gray-800">
            Available Packages
            {selectedPersons && <span className="text-sm font-normal text-gray-700 ml-2">({getFilteredPackages().length} packages found)</span>}
          </h2>

          <div className="flex items-center space-x-2">
            <span className="text-gray-700">Sort by:</span>
            <select className="border border-gray-300 rounded-md px-3 py-1.5 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>Price: Low to High</option>
              <option>Price: High to Low</option>
              <option>Duration: Short to Long</option>
              <option>Duration: Long to Short</option>
            </select>
          </div>
        </div>

        {/* Packages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {getFilteredPackages().map(pkg => (
            <div
              key={pkg.id}
              className={`group relative ${
                !selectedPersons
                  ? 'opacity-40 pointer-events-none'
                  : ''
              }`}
            >
              {/* Card with hover effect */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg transform transition-all duration-300 group-hover:-translate-y-2 group-hover:shadow-xl h-full flex flex-col">
                {/* Package Image with Overlay */}
                <div className="relative h-64">
                  <img
                    src={pkg.image}
                    alt={pkg.location}
                    className="w-full h-full object-cover"
                  />

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>

                  {/* Price tag */}
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-md">
                    <span className="font-bold text-gray-900">{pkg.price}</span>
                  </div>

                  {/* Duration tag */}
                  <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm flex items-center">
                    <i className="fas fa-clock mr-1.5"></i>
                    <span>{pkg.duration}</span>
                  </div>

                  {/* Location */}
                  <div className="absolute bottom-0 left-0 right-0 p-5">
                    <h3 className="text-white text-2xl font-bold mb-1">{pkg.location}</h3>
                    <div className="flex items-center text-white/80 text-sm">
                      <i className="fas fa-map-marker-alt mr-1.5"></i>
                      <span>North East India</span>
                    </div>
                  </div>
                </div>

                {/* Package Content */}
                <div className="p-5 flex-grow flex flex-col">
                  {/* Group size */}
                  <div className="inline-flex items-center bg-gray-100 px-3 py-1.5 rounded-full self-start mb-4">
                    <i className="fas fa-users text-gray-700 mr-1.5"></i>
                    <span className="text-sm text-gray-700">
                      {pkg.no_of_persons} {pkg.no_of_persons === "1" ? "Person" : "Persons"}
                    </span>
                  </div>

                  {/* Amenities */}
                  <div className="mb-5 flex-grow">
                    <h4 className="text-sm font-medium text-gray-700 uppercase tracking-wider mb-3">Includes</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center text-gray-700">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                          <i className="fas fa-car text-gray-700"></i>
                        </div>
                        <span>Private Transportation</span>
                      </li>
                      <li className="flex items-center text-gray-700">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                          <i className="fas fa-hotel text-gray-700"></i>
                        </div>
                        <span>Accommodation</span>
                      </li>
                      <li className="flex items-center text-gray-700">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                          <i className="fas fa-utensils text-gray-700"></i>
                        </div>
                        <span>Meals Included</span>
                      </li>
                    </ul>
                  </div>

                  {/* Action Button */}
                  <button
                    className="w-full py-3 bg-blue-400 text-white font-medium rounded-xl hover:bg-blue-500 transition-colors flex items-center justify-center"
                    onClick={() => handleCheckDetails(pkg.id)}
                  >
                    <span>View Package Details</span>
                    <i className="fas fa-arrow-right ml-2"></i>
                  </button>
                </div>
              </div>

              {/* "Best Value" badge for some packages */}
              {pkg.id % 3 === 0 && (
                <div className="absolute -top-3 -right-3 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg z-10">
                  Best Value
                </div>
              )}
            </div>
          ))}
        </div>

        {/* No Results Message */}
        {getFilteredPackages().length === 0 && selectedPersons && (
          <div className="text-center py-12">
            <div className="bg-gray-100 inline-block p-4 rounded-full mb-4">
              <i className="fas fa-search text-gray-400 text-2xl"></i>
            </div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">No packages found</h3>
            <p className="text-gray-600">
              Try selecting a different number of travelers or clearing your search filters.
            </p>
          </div>
        )}

        {/* Call to Action */}
        <div id="custom" className="mt-16 bg-blue-400 rounded-xl p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Need a Custom Package?</h2>
          <p className="max-w-2xl mx-auto mb-6">
            Can't find what you're looking for? Contact us to create a personalized travel experience tailored to your preferences.
          </p>
          <button
            className="bg-white text-gray-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50 transition-colors"
            onClick={() => navigate('/contact')}
          >
            Contact Us
          </button>
        </div>
      </div>
    </div>
  );
};

export default Plan;
