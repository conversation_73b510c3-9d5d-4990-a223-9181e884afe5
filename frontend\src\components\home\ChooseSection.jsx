import React from 'react'

const ChooseSection = () => {
  return (
    <div>
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white font-serif">
              Why Choose TrypIndia
            </h2>
            <div className="w-20 h-1 bg-blue-400 mx-auto my-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-blue-400 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-map-marked-alt text-blue-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">
                  Local Expertise
                </h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  Our team consists of local experts who know every hidden gem
                  and can provide authentic insights.
                </p>
              </div>
            </div>

            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-blue-400 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-hand-holding-heart text-blue-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">
                  Personalized Service
                </h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  We tailor each journey to your preferences, ensuring a unique
                  and memorable experience.
                </p>
              </div>
            </div>

            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-blue-400 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-shield-alt text-blue-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">
                  Safety First
                </h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  Your safety is our priority. We follow strict protocols and
                  work with trusted partners.
                </p>
              </div>
            </div>

            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-blue-400 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-leaf text-blue-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">
                  Responsible Tourism
                </h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  We're committed to sustainable practices that respect local
                  communities and the environment.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default ChooseSection