import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import AOS from 'aos';
import { blogPosts } from '../../data/blogPosts';
import profile1 from '../../assets/photos/profile1.webp';

const Blog_Details = () => {
  const { id } = useParams();
  const [blogPost, setBlogPost] = useState(null);

  

  const relatedPosts = blogPosts.filter(post => post.id !== parseInt(id)).slice(0, 3);
  const popularPosts = [...blogPosts].sort((a, b) => b.views - a.views).slice(0, 4);

  useEffect(() => {
    // Find the blog post by ID
    const post = blogPosts.find(p => p.id === parseInt(id));
    setBlogPost(post);

    if (typeof AOS !== 'undefined') {
      AOS.init({
        duration: 1000,
        once: true,
      });
    }
    window.scrollTo(0, 0);
  }, [id]);

  if (!blogPost) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Blog Post Not Found</h2>
          <Link to="/blog" className="text-blue-600 hover:underline">
            Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="blog-details-page min-h-screen bg-gray-50 dark:bg-gray-900 pt-20">
      {/* Hero Section */}
      <section className="relative text-black dark:text-white py-20">
        <div className="absolute inset-0"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div 
            className="mb-6"
            data-aos="fade-up"
          >
            <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium">
              {blogPost.category}
            </span>
          </div>
          <h1 
            className="text-4xl md:text-5xl font-bold mb-6"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            {blogPost.title}
          </h1>
          <div 
            className="flex items-center justify-center space-x-6 text-black dark:text-white"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <div className="flex items-center">
              <img
                src={blogPost.authorImage}
                alt={blogPost.author}
                className="w-10 h-10 rounded-full mr-3"
              />
              <span>{blogPost.author}</span>
            </div>
            <span>•</span>
            <span>{blogPost.date}</span>
            <span>•</span>
            <span>{blogPost.readTime}</span>
            <span>•</span>
            <span className="flex items-center">
              <i className="fas fa-eye mr-1"></i>
              {blogPost.views}
            </span>
          </div>
        </div>
      </section>

      {/* Main Content with Sidebar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content */}
          <div className="lg:w-2/3">
            {/* Article Content */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden" data-aos="fade-up">
              {/* Featured Image */}
              <div className="relative h-96">
                <img
                  src={blogPost.image}
                  alt={blogPost.title}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Article Body */}
              <div className="p-8 md:p-12">
                <div 
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: blogPost.content }}
                />

                {/* Article Stats */}
                <div className="mt-8 pt-8 border-t border-gray-200 flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                      <i className="fas fa-heart"></i>
                      <span>{blogPost.likes}</span>
                    </button>
                    <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                      <i className="fas fa-comment"></i>
                      <span>24</span>
                    </button>
                    <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                      <i className="fas fa-share"></i>
                      <span>Share</span>
                    </button>
                  </div>
                  <div className="flex items-center text-gray-500">
                    <i className="fas fa-eye mr-2"></i>
                    <span>{blogPost.views} views</span>
                  </div>
                </div>

                {/* Tags */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tags:</h3>
                  <div className="flex flex-wrap gap-2">
                    {blogPost.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 cursor-pointer transition-colors"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Social Sharing */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Share this article:</h3>
                  <div className="flex space-x-4">
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      <i className="fab fa-facebook-f mr-2"></i>
                      Facebook
                    </button>
                    <button className="bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors">
                      <i className="fab fa-twitter mr-2"></i>
                      Twitter
                    </button>
                    <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                      <i className="fab fa-whatsapp mr-2"></i>
                      WhatsApp
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Author Section */}
            <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-md p-8" data-aos="fade-up">
              <div className="flex items-start space-x-6">
                <img
                  src={blogPost.authorImage}
                  alt={blogPost.author}
                  className="w-20 h-20 rounded-full"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{blogPost.author}</h3>
                  <p className="text-gray-600 mb-4">Travel Writer & Photographer</p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    {blogPost.author} is a passionate travel writer with over 10 years of experience exploring the Himalayas. 
                    Specializing in cultural tourism and adventure travel, they bring unique perspectives and insights to 
                    every destination they cover.
                  </p>
                  <div className="flex space-x-4">
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      Follow
                    </button>
                    <button className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                      View Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Comments Section */}
            <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-md p-8" data-aos="fade-up">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Comments (24)</h3>
              <div className="space-y-6">
                {/* Sample Comment */}
                <div className="flex space-x-4">
                  <img
                    src={profile1}
                    alt="User"
                    className="w-12 h-12 rounded-full"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white">John Doe</h4>
                      <span className="text-gray-500 text-sm">2 days ago</span>
                    </div>
                    <p className="text-gray-700 mb-2">
                      Amazing article! I've always wanted to visit Darjeeling and this just made me want to go even more. 
                      The tea gardens sound absolutely magical.
                    </p>
                    <button className="text-blue-600 text-sm hover:underline">Reply</button>
                  </div>
                </div>
              </div>
              
              {/* Comment Form */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Leave a Comment</h4>
                <textarea
                  placeholder="Write your comment here..."
                  className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="4"
                ></textarea>
                <button className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Post Comment
                </button>
              </div>
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="lg:w-1/3 space-y-8">
            {/* Author Profile Card */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6" data-aos="fade-left">
              <div className="text-center">
                <img
                  src={blogPost.authorImage}
                  alt={blogPost.author}
                  className="w-20 h-20 rounded-full mx-auto mb-4"
                />
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{blogPost.author}</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Travel Writer & Photographer
                </p>
                <div className="flex justify-center space-x-4 mb-4">
                  <div className="text-center">
                    <div className="font-bold text-gray-900 dark:text-white">156</div>
                    <div className="text-xs text-gray-500">Posts</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-gray-900 dark:text-white">2.4K</div>
                    <div className="text-xs text-gray-500">Followers</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-gray-900 dark:text-white">89</div>
                    <div className="text-xs text-gray-500">Following</div>
                  </div>
                </div>
                <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Follow Author
                </button>
              </div>
            </div>

            {/* Table of Contents */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6" data-aos="fade-left" data-aos-delay="100">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Table of Contents</h3>
              <nav className="space-y-2">
                <a href="#journey" className="block text-gray-600 hover:text-blue-600 transition-colors">The Journey Begins</a>
                <a href="#tea-gardens" className="block text-gray-600 hover:text-blue-600 transition-colors">Tea Gardens and Traditions</a>
                <a href="#culture" className="block text-gray-600 hover:text-blue-600 transition-colors">Cultural Melting Pot</a>
                <a href="#adventure" className="block text-gray-600 hover:text-blue-600 transition-colors">Adventure Awaits</a>
                <a href="#cuisine" className="block text-gray-600 hover:text-blue-600 transition-colors">Local Cuisine and Flavors</a>
                <a href="#planning" className="block text-gray-600 hover:text-blue-600 transition-colors">Planning Your Visit</a>
              </nav>
            </div>

            {/* Related Posts */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6" data-aos="fade-left" data-aos-delay="200">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Related Articles</h3>
              <div className="space-y-4">
                {relatedPosts.map((post) => (
                  <Link key={post.id} to={`/blog/${post.id}`} className="group">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          src={post.image}
                          alt={post.title}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                          {post.title}
                        </h4>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <span>{post.views} views</span>
                          <span className="mx-1">•</span>
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Popular Posts */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6" data-aos="fade-left" data-aos-delay="300">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Popular Posts</h3>
              <div className="space-y-4">
                {popularPosts.map((post) => (
                  <Link key={post.id} to={`/blog/${post.id}`} className="group">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          src={post.image}
                          alt={post.title}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                          {post.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">{post.views} views</p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Newsletter Signup */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-md p-6 text-white" data-aos="fade-left" data-aos-delay="400">
              <h3 className="text-lg font-bold mb-2">Stay Updated</h3>
              <p className="text-blue-100 text-sm mb-4">
                Get the latest travel stories and tips delivered to your inbox
              </p>
              <div className="space-y-3">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full px-4 py-3 rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-white dark:focus:ring-gray-500"
                />
                <button className="w-full bg-white text-blue-600 px-4 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                  Subscribe
                </button>
              </div>
            </div>

            {/* Tags Cloud */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6" data-aos="fade-left" data-aos-delay="500">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Popular Tags</h3>
              <div className="flex flex-wrap gap-2">
                {['Darjeeling', 'Himalayas', 'Tea Gardens', 'Culture', 'Adventure', 'Wildlife', 'Monasteries', 'Trekking', 'Photography', 'Travel Tips'].map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 cursor-pointer transition-colors"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Reading Progress */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6" data-aos="fade-left" data-aos-delay="600">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Reading Progress</h3>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
              </div>
              <p className="text-sm text-gray-600">45% completed</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog_Details;
