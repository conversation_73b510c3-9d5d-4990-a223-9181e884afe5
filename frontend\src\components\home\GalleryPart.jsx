import * as React from "react";
import Box from "@mui/material/Box";
import ImageList from "@mui/material/ImageList";
import ImageListItem from "@mui/material/ImageListItem";

import nature2 from "../../assets/photos/nature2.webp";
import city_gangtok from "../../assets/photos/city_gangtok.webp";
import plan2 from "../../assets/photos/plan2.png";
import card2 from "../../assets/photos/card2.webp";
import dooars from "../../assets/photos/dooars.webp";
import darjeeling from "../../assets/photos/dargeeling2.webp";
import tea from "../../assets/photos/tea.webp";
import culture1 from "../../assets/photos/culture1.webp";
import monks4 from "../../assets/photos/monks4.webp";
import { Link } from "react-router-dom";


export default function MasonryImageList() {
  return (
    <div className="py-24 bg-white dark:bg-gray-900 relative">
      <div className="absolute inset-0"></div>
      <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-60 bg-white dark:bg-gray-900"></div>
      <div className="max-w-5xl mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <span className="bg-indigo-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wider">
            Gallery
          </span>
          <h2 className="text-3xl md:text-4xl font-light mt-4 mb-6 text-gray-900 font-serif">
            Photo <span className="text-blue-400">Gallery</span>
          </h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover the beauty of North East India through our collection of
            photographs
          </p>
        </div>
        <Box sx={{ overflowY: "auto", maxHeight: 450 }}>
          <ImageList variant="masonry" cols={4} gap={8}>
            {itemData.map((item) => (
              <ImageListItem key={item.img}>
                <img
                  src={`${item.img}?w=248&fit=crop&auto=format`}
                  srcSet={`${item.img}?w=248&fit=crop&auto=format&dpr=2 2x`}
                  alt={item.title}
                  loading="lazy"
                />
              </ImageListItem>
            ))}
          </ImageList>
        </Box>
        <div className="text-center mt-12">
          <Link to="/gallery">
            <button className="inline-block border border-gray-900 px-8 py-3 text-sm uppercase tracking-widest text-gray-900 hover:bg-blue-400 hover:border-blue-400 hover:text-white transition-colors duration-300">
              View More
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}

    
  

const itemData = [
  {
    img: nature2,
    title: "Kanchenjunga Range",
  },
  {
    img: city_gangtok,
    title: "Gangtok City View",
  },
  {
    img: plan2,
    title: "North Sikkim Mountains",
  },
  {
    img: card2,
    title: "Darjeeling View",
  },
  {
    img: dooars,
    title: "Dooars",
  },
  {
    img: darjeeling,
    title: "Darjeeling Tea Garden",
  },
  {
    img: tea,
    title: "Darjeeling Tea",
  },
  {
    img: culture1,
    title: "Monks in Sikkim",
  },
  {
    img: monks4,
    title: "Monks in Sikkim",
  },
];
