import { useState } from 'react';
import animation11 from '../../assets/photos/animation11.png';


const VideoSection = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  // YouTube video configuration
  const youtubeId = 'rvmEz9RiDZ8'; // replace with your desired YouTube video ID
  const poster = animation11;

  const handlePlay = () => {
    setIsPlaying(true);
  };

  return (
    <section className="py-0 bg-white dark:bg-gray-900">
      <div className="px-0">
        <div className="max-w-7xl mx-auto text-center md:mb-14">
          <h2 className="text-4xl md:text-5xl font-light mb-4 text-gray-900 font-serif">
            Discover the{" "}
            <span className='text-blue-400'>Magic</span> of North East India
          </h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-xl mx-auto">
            Discover the beauty of North East India through our collection of
            photographs
          </p>
        </div>
        <div className="w-full h-auto ">
          <div className="relative overflow-hidden">
            {isPlaying ? (
              <div className="relative w-full" style={{ paddingTop: "56.25%" }}>
                <iframe
                  className="absolute inset-0 w-full h-full"
                  src={`https://www.youtube.com/embed/${youtubeId}?autoplay=1&rel=0&modestbranding=1&playsinline=1&controls=1`}
                  title="YouTube video player"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                ></iframe>
              </div>
            ) : (
              <>
                <img
                  src={poster}
                  alt="Video poster"
                  className="w-full h-auto"
                />
                <button
                  aria-label="Play video"
                  onClick={handlePlay}
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                >
                  <span className="w-24 h-24 md:w-28 md:h-28 bg-white/70 hover:bg-white/80 rounded-full grid place-items-center transition">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M8 5v14l11-7-11-7z" fill="#111827" />
                    </svg>
                  </span>
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default VideoSection;
