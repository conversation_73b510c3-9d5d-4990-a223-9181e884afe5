import { Link } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';

// Import local images
import darjeelingImg from '../../assets/photos/dargeeling2.webp';
import gangtokImg from '../../assets/photos/city_gangtok.webp';
import northSikkimImg from '../../assets/photos/paranoma1.webp';

import mountainImg from '../../assets/photos/paranoma1.webp'; // Mountain image for contact
import teaImg from '../../assets/photos/dargeeling_tea.webp';
import monksImg from '../../assets/photos/monks4.webp';
import nature2 from '../../assets/photos/nature2.webp';

const DestinationsSection = () => {
  const [activeIndex, setActiveIndex] = useState(2); // Start from middle (index 2)
  const [startX, setStartX] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const coverflowRef = useRef(null);

  const destinations = [
    {
      id: 1,
      name: 'Darjeeling',
      image: darjeelingImg,
      secondaryImage: teaImg,
      description: 'Experience the charm of tea gardens and panoramic Himalayan views',
      highlights: ['Tiger Hill Sunrise', 'Toy Train Ride', 'Tea Gardens', 'Himalayan Mountaineering Institute'],
    },
    {
      id: 2,
      name: 'Gangtok',
      image: gangtokImg,
      secondaryImage: monksImg,
      description: 'Explore the vibrant capital city of Sikkim with its monasteries and viewpoints',
      highlights: ['Nathula Pass', 'Rumtek Monastery', 'MG Marg', 'Banjhakri Falls'],
    },
    {
      id: 3,
      name: 'North Sikkim',
      image: nature2,
      secondaryImage: nature2,
      description: 'Discover the pristine beauty of Lachen and Lachung in North Sikkim',
      highlights: ['Gurudongmar Lake', 'Yumthang Valley', 'Zero Point', 'Lachung Monastery'],
    },
    {
      id: 4,
      name: 'Lachen',
      image: mountainImg,
      secondaryImage: mountainImg,
      description: 'Enjoy spectacular views of Kanchenjunga and ancient monasteries',
      highlights: ['Kanchenjunga View', 'Pemayangtse Monastery', 'Rabdentse Ruins', 'Khecheopalri Lake'],
    },
    {
      id: 5,
      name: 'Lachung',
      image: teaImg, // Using tea image as a placeholder
      secondaryImage: teaImg,
      description: 'Explore the lush forests, wildlife sanctuaries and tea gardens of Dooars',
      highlights: ['Jaldapara National Park', 'Gorumara National Park', 'Buxa Tiger Reserve', 'Chapramari Wildlife Sanctuary'],
    },
  ];

  const handlePrev = () => {
    setActiveIndex((prev) => (prev === 0 ? destinations.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === destinations.length - 1 ? 0 : prev + 1));
  };

  const handleMouseDown = (e) => {
    setStartX(e.pageX);
    setIsDragging(true);
  };

  const handleTouchStart = (e) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const currentX = e.pageX;
    handleSwipe(currentX);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const currentX = e.touches[0].clientX;
    handleSwipe(currentX);
  };

  const handleSwipe = (currentX) => {
    if (startX === null) return;

    const diff = startX - currentX;
    if (diff > 50) {
      handleNext();
      setIsDragging(false);
      setStartX(null);
    } else if (diff < -50) {
      handlePrev();
      setIsDragging(false);
      setStartX(null);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setStartX(null);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    setStartX(null);
  };

  // Remove auto-rotation for better user experience
  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     handleNext();
  //   }, 4000);
  //   return () => clearInterval(interval);
  // }, [activeIndex]);

  const getCardStyle = (index) => {
    const distance = index - activeIndex;
    const absDistance = Math.abs(distance);
    
    // Only show cards within reasonable range
    if (absDistance > 2) return { display: 'none' };

    const translateX = distance * 80; // Horizontal offset
    const translateZ = -absDistance * 50; // Depth
    const rotateY = distance * 15; // Rotation
    const scale = 1 - absDistance * 0.15; // Scale
    const opacity = 1 - absDistance * 0.4; // Opacity

    return {
      transform: `translateX(${translateX}%) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(${scale})`,
      opacity: Math.max(opacity, 0.3),
      zIndex: 10 - absDistance,
    };
  };

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <div className="text-xs uppercase tracking-widest text-gray-500 mb-3 font-serif">
            Discover
          </div>
          <h2 className="text-5xl font-light mb-4 text-gray-900 dark:text-white font-serif">
            Top
            <span className='text-blue-400'> Destinations</span>
          </h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-xl mx-auto">
            Explore the most breathtaking locations in North East India
          </p>
        </div>

        {/* Simplified Coverflow Effect */}
        <div
          className="relative h-[550px] overflow-hidden"
          ref={coverflowRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className="absolute inset-0 flex items-center justify-center max-h-[500px]">
            {destinations.map((destination, index) => (
              <div
                key={destination.id}
                className="absolute w-[340px] h-[500px] rounded-2xl transition-all duration-700 ease-out bg-white dark:bg-gray-800 overflow-hidden cursor-pointer"
                style={{
                  ...getCardStyle(index),
                  boxShadow: index === activeIndex
                    ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                    : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                }}
                onClick={() => setActiveIndex(index)}
              >
                <div className="h-[250px] overflow-hidden relative">
                  <img
                    src={destination.image}
                    alt={destination.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-0 left-0 bg-indigo-500 text-white text-xs tracking-wider py-1 px-3">
                    FEATURED
                  </div>
                </div>

                <div className="p-8">
                  <div className="mb-2 text-xs text-gray-500 uppercase tracking-wider">
                    North East India
                  </div>
                  <h3 className="text-2xl font-light text-gray-900 dark:text-white mb-4 border-b border-gray-100 dark:border-gray-700 pb-4">
                    {destination.name}
                  </h3>

                  <p className="text-gray-600 mb-6 text-sm leading-relaxed">
                    {destination.description}
                  </p>

                  <div className="mb-6">
                    <div className="text-xs uppercase tracking-wider text-gray-500 mb-3">
                      Highlights
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {destination.highlights
                        .slice(0, 3)
                        .map((highlight, idx) => (
                          <span
                            key={idx}
                            className="inline-block bg-gray-50 border border-gray-100 px-2 py-1 text-xs text-gray-700"
                          >
                            {highlight}
                          </span>
                        ))}
                    </div>
                  </div>

                  <Link
                    to="/plan"
                    className="inline-block border border-gray-900 dark:border-gray-300 text-gray-900 dark:text-gray-300 px-6 py-2 text-sm uppercase tracking-wider hover:bg-gray-900 hover:text-white dark:hover:bg-gray-300 dark:hover:text-gray-900 transition-colors duration-300"
                  >
                    Discover
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Controls */}
          <button
            onClick={handlePrev}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white border border-gray-200 text-gray-800 w-10 h-10 flex items-center justify-center z-50 hover:bg-gray-900 hover:text-white hover:border-gray-900 transition-colors duration-300 shadow-sm"
          >
            <i className="fas fa-chevron-left"></i>
          </button>

          <button
            onClick={handleNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white border border-gray-200 text-gray-800 w-10 h-10 flex items-center justify-center z-50 hover:bg-gray-900 hover:text-white hover:border-gray-900 transition-colors duration-300 shadow-sm"
          >
            <i className="fas fa-chevron-right"></i>
          </button>

          {/* Dots Indicator */}
          <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 flex space-x-1 z-50">
            {destinations.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === activeIndex ? 'bg-gray-900' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        <div className="text-center mt-16">
          <Link
            to="/plan"
            className="inline-block border border-gray-900 px-8 py-3 text-sm uppercase tracking-widest text-gray-900 hover:bg-blue-400 hover:border-blue-400 hover:text-white transition-colors duration-300"
          >
            View All Destinations
          </Link>
        </div>
      </div>
    </section>
  );
};

export default DestinationsSection;
