import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import AOS from 'aos';

// Import images
import wildlife_redpanda from '../assets/photos/wildlife_redpanda.webp';
import tea from '../assets/photos/tea.webp';
import city_darjeeling from '../assets/photos/city_dargeeling.webp';
import forest from '../assets/photos/forest.webp';

const Blog = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [sortBy, setSortBy] = useState('date'); // date, popularity, title

  // Sample blog data
  const blogPosts = [
    {
      id: 1,
      title: "Pack wisely before traveling",
      excerpt: "A wonderful serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with my whole heart. I am alone, and feel the charm of existence in this spot, which was created for the bliss of souls like mine. I am so happy, my dear friend, so absorbed in the exquisite sense of mere tranquil existence, that I neglect my talents. I should be...",
      category: "Travel Tips",
      author: "<PERSON>",
      date: "June 6, 2024",
      readTime: "5 min read",
      image: forest,
      featured: true,
      sticky: true,
      views: 1247,
      comments: 8
    },
    {
      id: 2,
      title: "Discovering the Hidden Treasures of Darjeeling",
      excerpt: "Explore the mystical hills of Darjeeling, where every turn reveals breathtaking views and rich cultural heritage. The tea gardens stretch as far as the eye can see, creating a mesmerizing patchwork of emerald hues that dance in the mountain breeze...",
      category: "Destinations",
      author: "Travel Expert",
      date: "December 15, 2024",
      readTime: "5 min read",
      image: city_darjeeling,
      featured: false,
      sticky: false,
      views: 892,
      comments: 12
    },
    {
      id: 3,
      title: "The Art of Tea Making in the Himalayas",
      excerpt: "Dive into the centuries-old tradition of tea cultivation and the fascinating process behind Darjeeling's world-famous tea. Each garden has its own story, its own unique blend, and its own dedicated workers who have been perfecting the art for generations...",
      category: "Culture",
      author: "Tea Connoisseur",
      date: "December 12, 2024",
      readTime: "4 min read",
      image: tea,
      featured: false,
      sticky: false,
      views: 567,
      comments: 5
    },
    {
      id: 4,
      title: "Wildlife Encounters: Spotting the Red Panda",
      excerpt: "Venture into the dense forests of Sikkim to catch a glimpse of the elusive and adorable red panda in its natural habitat. The experience of seeing these rare creatures in the wild is truly unforgettable...",
      category: "Wildlife",
      author: "Wildlife Photographer",
      date: "December 10, 2024",
      readTime: "6 min read",
      image: wildlife_redpanda,
      featured: false,
      sticky: false,
      views: 445,
      comments: 3
    },
  ];

  const categories = [
    { name: 'All', count: blogPosts.length },
    { name: 'Destinations', count: blogPosts.filter(post => post.category === 'Destinations').length },
    { name: 'Culture', count: blogPosts.filter(post => post.category === 'Culture').length },
    { name: 'Wildlife', count: blogPosts.filter(post => post.category === 'Wildlife').length },
    { name: 'Adventure', count: blogPosts.filter(post => post.category === 'Adventure').length },
    { name: 'Travel Tips', count: blogPosts.filter(post => post.category === 'Travel Tips').length }
  ];

  const filteredPosts = (activeCategory === 'All'
    ? blogPosts
    : blogPosts.filter(post => post.category === activeCategory))
    .sort((a, b) => {
      switch (sortBy) {
        case 'popularity':
          return b.views - a.views;
        case 'title':
          return a.title.localeCompare(b.title);
        case 'date':
        default:
          return new Date(b.date) - new Date(a.date);
      }
    });

  const recentComments = [
    { author: "John Doe", post: "Discovering the Hidden Treasures of Darjeeling" },
    { author: "Jane Smith", post: "The Art of Tea Making in the Himalayas" },
  ];

  const tags = ["Article", "City", "Nature", "News", "Night", "Park", "Resort", "Tour", "Travel", "Vacation", "Adventure", "Culture", "Himalayas", "Tea", "Wildlife"];

  useEffect(() => {
    if (typeof AOS !== 'undefined') {
      AOS.init({
        duration: 1000,
        once: true,
      });
    }
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="blog-page min-h-screen bg-gray-50 dark:bg-gray-900 pt-20">
      {/* Hero Section */}
      <section className="bg-white dark:bg-gray-800 py-16 border-b dark:border-gray-700 mb-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6"
            data-aos="fade-up"
          >
            Travel Blog
          </h1>
          <p 
            className="text-xl text-gray-600 max-w-3xl mx-auto"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            Discover stories, tips, and insights from the heart of the Himalayas
          </p>
        </div>
      </section>

      {/* Sort Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-700 dark:text-gray-300 font-medium">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="date">Latest First</option>
              <option value="popularity">Most Popular</option>
              <option value="title">Alphabetical</option>
            </select>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredPosts.length} {filteredPosts.length === 1 ? 'post' : 'posts'}
          </div>
        </div>
      </div>

      {/* Main Content with Sidebar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content */}
          <div className="lg:w-2/3">
            {/* Blog Posts */}
            <div className="space-y-8">
              {filteredPosts.map((post, index) => (
                <article
                  key={post.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  {/* Featured Image */}
                  <div className="relative">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-64 object-cover"
                    />
                    {post.sticky && (
                      <div className="absolute bottom-4 left-4">
                        <span className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium flex items-center">
                          <i className="fas fa-bolt mr-1"></i>
                          STICKY POST
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Post Content */}
                  <div className="p-6">
                    {/* Post Metadata */}
                    <div className="flex items-center text-sm text-gray-500 mb-4 space-x-4">
                      <span className="flex items-center">
                        <i className="far fa-clock mr-1"></i>
                        {post.date}
                      </span>
                      <span className="flex items-center">
                        <i className="far fa-file-alt mr-1"></i>
                        {post.author}
                      </span>
                      <span className="flex items-center">
                        <i className="fas fa-tags mr-1"></i>
                        {post.category}
                      </span>
                      <span className="flex items-center">
                        <i className="far fa-comment mr-1"></i>
                        {post.comments}
                      </span>
                    </div>

                    {/* Post Title */}
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      {post.title}
                    </h2>

                    {/* Post Excerpt */}
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                      {post.excerpt}
                    </p>

                    {/* Read More Button */}
                    <Link 
                      to={`/blog/${post.id}`}
                      className="inline-block bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors"
                    >
                      Read More
                    </Link>
                  </div>
                </article>
              ))}
            </div>

            {/* Pagination */}
            <div className="mt-12 flex justify-center">
              <nav className="flex items-center space-x-2">
                <button className="px-4 py-2 text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50">
                  Previous
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded">1</button>
                <button className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50">2</button>
                <button className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50">3</button>
                <button className="px-4 py-2 text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50">
                  Next
                </button>
              </nav>
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="lg:w-1/3 space-y-8">
            {/* Text Widget */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Text Widget</h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Nulla vitae elit libero, a pharetra augue. Nulla vitae elit libero, a pharetra augue. 
                Nulla vitae elit libero, a pharetra augue. Donec sed odio dui. Etiam porta sem malesuada.
              </p>
            </div>

            {/* Recent Comments */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Recent Comments</h3>
              <div className="space-y-3">
                {recentComments.map((comment, index) => (
                  <div key={index} className="text-sm">
                    <span className="font-medium text-gray-900 dark:text-white">{comment.author}</span>
                    <span className="text-gray-500 dark:text-gray-400"> on </span>
                    <span className="text-blue-600 hover:underline cursor-pointer">{comment.post}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Tag Cloud */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Tag Cloud</h3>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded text-sm hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 cursor-pointer transition-colors"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.slice(1).map((category) => (
                  <button
                    key={category.name}
                    onClick={() => setActiveCategory(category.name)}
                    className="w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                  >
                    <span className="text-gray-700 dark:text-gray-300">{category.name}</span>
                    <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog;