import { useState } from "react";
import { useNavigate } from "react-router-dom";
import login6 from "../../assets/photos/login6.jpg";

const HeroSection = () => {
  const [month, setMonth] = useState("Any");
  const [destination, setDestination] = useState("Any");
  const [person, setDuration] = useState("Any");
  const navigate = useNavigate();

  const months = [
    "Any",
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const destinations = [
    "Any",
    "Darjeeling",
    "Gangtok",
    "North Sikkim",
    "Lachen",
    "Lachung",
    
  ];

  const persons = [
    "Any",
    "1",
    "2",
    "3",
    "4",
    "4+",
  ];

  const handleSearchSubmit = (e) => {
    //search logic 
    e.preventDefault();//prevents page refresh when form is submitted
    const params = new URLSearchParams(); //creates a new URLSearchParams object
    if (month.trim()) params.append("month", month); //if month is not empty, add it to the URLSearchParams object
    if (destination !== "Any") //if destination is not "Any", add it to the URLSearchParams object
      params.append("destination", destination.toLowerCase());// convert destination to lowercase before adding it to the URLSearchParams object
    if (person !== "Any") params.append("persons", person); //if duration is not "Any", add it to the URLSearchParams object

    navigate(`/plan?${params.toString()}`);//navigate to the plan page with the URLSearchParams object as query parameters
  };
  // this is to disable the search button if no search parameters are selected
  const isSearchEnabled = month.trim()!== "Any" || destination !== "Any" || person !== "Any";

  return (
    <div className="hero-section">
      <div className="relative bg-white dark:bg-gray-900 pt-[160px] pb-[100px]">
        {/* Hero Section */}
        <div className="relative" style={{ zIndex: 1 }}>
          {/* Content Container */}
          <div className="max-w-7xl mx-auto px-6 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-10 items-start">
              {/* Left Side - Content */}
              <div className="lg:col-span-9 mt-10" style={{ zIndex: 3 }}>
                <div className="max-w-4xl">
                  {/* Book With Us Badge */}
                  <div className="inline-block mb-6">
                    <div className="inline-flex items-center justify-center text-sm font-medium text-green-500 bg-green-50 dark:bg-green-900 px-4 py-2 rounded-[25px] border border-green-100 dark:border-green-800">
                      Book With Us!
                    </div>
                  </div>

                  {/* Main Heading */}
                  <h1 className="mb-6 font-serif text-[65px] font-semibold tracking-[-0.02em] text-gray-800 dark:text-white leading-[1.1]">
                    Get Travel Budget <br />
                    <span className="text-blue-400">in Minutes!</span>
                  </h1>

                  {/* Description */}
                  <div className="mb-12 py-5 font-sans text-[22px] text-gray-500 dark:text-gray-300 leading-[1.6]">
                    <p>Discover amazing places at exclusive deals.</p>
                    <p>Eat, Shop, Visit interesting places across the Himalayas.</p>
                  </div>

                  {/* Search Form */}
                  <div className="w-full">
                    <div className="bg-white dark:bg-gray-800 rounded-2xl p-0 shadow-[0_20px_35px_rgba(20,20,20,0.08)] dark:shadow-[0_20px_35px_rgba(0,0,0,0.3)]">
                      <form
                        onSubmit={handleSearchSubmit}
                        className="grid grid-cols-1 md:grid-cols-4 gap-0"
                      >
                        {/* Keywords Input */}
                        <div className="p-6 border-r border-gray-100 dark:border-gray-700">
                          <label className="block text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                            Travel Month
                          </label>
                          <select
                            name="tour-search"
                            type="text"
                            placeholder="Type Your Keywords"
                            value={month}
                            onChange={(e) => setMonth(e.target.value)} // this is to update the month state when the user selects a month
                            className="w-full border-0 outline-none text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-[16px]"
                          >
                            // this is to map through the months array and
                            display each month as an option
                            {months.map((month) => (
                              <option key={month} value={month}>
                                {month}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Destination Dropdown */}
                        <div className="p-6 border-r border-gray-100 dark:border-gray-700">
                          <label className="block text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                            Destination
                          </label>
                          <select
                            name="tax-tour-destination"
                            value={destination}
                            onChange={(e) => setDestination(e.target.value)}
                            className="w-full border-0 outline-none text-gray-800 dark:text-gray-200 bg-transparent text-[16px]"
                          >
                            {destinations.map((dest) => (
                              <option key={dest} value={dest}>
                                {dest}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Persons Dropdown */}
                        <div className="p-6 border-r border-gray-100 dark:border-gray-700">
                          <label className="block text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                             No of Persons
                          </label>
                          <select
                            name="duration"
                            value={person}
                            onChange={(e) => setDuration(e.target.value)}
                            className="w-full border-0 outline-none text-gray-800 dark:text-gray-200 bg-transparent text-[16px]"
                          >
                            {persons.map((per) => (
                              <option key={per} value={per}>
                                {per}
                              </option>
                            ))}
                          </select>                          
                        </div>

                        {/* Search Button */}
                        <div className="p-0">
                          <button
                            type="submit"
                            disabled={!isSearchEnabled} // this is to disable the search button if no search parameters are selected
                            className={`w-full h-full min-h-[60px] h-120 bg-blue-400 text-white rounded-r-2xl font-semibold transition-all duration-300 flex items-center justify-center ${
                              !isSearchEnabled ? "cursor-not-allowed" : ""
                            }`}
                          >
                            <span className="flex items-center">
                              <svg
                                className="w-5 h-5 mr-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <span>Search Now</span>
                            </span>
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Image */}
              <div className="lg:col-span-3 relative hidden lg:block z-[2]">
                <div className="flex justify-center ml-[-330px]">
                  <div className="relative">
                    <img
                      src={login6}
                      alt="Beautiful mountain landscape with turquoise lake"
                      className="object-cover rounded-[20px] w-[505px] h-[650px]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
